<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <MicrosoftExtensionsAIVersion>9.4.0-preview.1.25207.5</MicrosoftExtensionsAIVersion>
    <!-- By having a common user secrets ID, it's possible to set your LLM service endpoint/key just once and use them for the whole workshop -->
    <WorkshopUserSecretsId>09d8a94b-1370-4bc7-a9b8-9326a60ed927</WorkshopUserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Azure.AI.OpenAI" Version="2.2.0-beta.4" />
    <PackageVersion Include="FaissMask" Version="0.3.2-beta02" />
    <PackageVersion Include="FaissNet" Version="1.1.0" />
    <PackageVersion Include="FluentAssertions" Version="7.0.0" />
    <PackageVersion Include="Microsoft.Extensions.AI" Version="$(MicrosoftExtensionsAIVersion)" />
    <PackageVersion Include="Microsoft.Extensions.AI.Abstractions" Version="$(MicrosoftExtensionsAIVersion)" />
    <PackageVersion Include="Microsoft.Extensions.AI.Ollama" Version="$(MicrosoftExtensionsAIVersion)" />
    <PackageVersion Include="Microsoft.Extensions.AI.OpenAI" Version="$(MicrosoftExtensionsAIVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.1" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Microsoft.SemanticKernel.Core" Version="1.34.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="PdfPig" Version="0.1.10-alpha-20241019-e1060" />
    <PackageVersion Include="Qdrant.Client" Version="1.13.0" />
    <PackageVersion Include="SmartComponents.LocalEmbeddings" Version="0.1.0-preview10148" />
    <PackageVersion Include="System.Numerics.Tensors" Version="9.0.1" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
  </ItemGroup>
</Project>