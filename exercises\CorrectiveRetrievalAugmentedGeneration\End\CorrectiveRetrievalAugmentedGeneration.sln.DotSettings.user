﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=3bd14a5f_002D05bf_002D499c_002Da25f_002D1e312314f6d3/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="AIParserTool_parses_a_conversation" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;xUnit::FC8E87F8-48DA-4711-9D0B-B084558D16CA::net9.0::Planner.Tests.StructuredChatClientTests.AIParserTool_parses_a_conversation&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>